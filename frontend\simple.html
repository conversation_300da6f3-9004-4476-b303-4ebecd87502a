<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICOM IC-R8600 Controller</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .card-title {
            font-size: 1.4rem;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .input, .select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .input:focus, .select:focus {
            border-color: #667eea;
            outline: none;
        }

        .button {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
            margin: 5px;
        }

        .button-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .button-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }

        .button-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .status-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 2px solid #e9ecef;
        }

        .status-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .status-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid;
        }

        .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .alert-error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- En-tête -->
        <div class="header">
            <h1 class="title">📻 ICOM IC-R8600 Controller</h1>
            <p class="subtitle">Interface de contrôle pour récepteur radiofréquence</p>
        </div>

        <!-- Messages d'alerte -->
        <div id="message" class="alert hidden"></div>

        <!-- Grille principale -->
        <div class="grid">
            
            <!-- Carte de contrôle principal -->
            <div class="card">
                <h2 class="card-title">⚙️ Contrôles Principal</h2>
                
                <div class="form-group">
                    <label class="label">Fréquence (Hz)</label>
                    <input type="number" id="frequency" class="input" value="145500000" placeholder="145500000">
                </div>
                
                <div class="form-group">
                    <label class="label">Mode de modulation</label>
                    <select id="mode" class="select">
                        <option value="LSB">LSB</option>
                        <option value="USB">USB</option>
                        <option value="AM">AM</option>
                        <option value="CW">CW</option>
                        <option value="FM" selected>FM</option>
                        <option value="WFM">WFM</option>
                        <option value="CWR">CWR</option>
                        <option value="RTTY">RTTY</option>
                        <option value="RTTYR">RTTYR</option>
                        <option value="PSK">PSK</option>
                        <option value="PSKR">PSKR</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="label">RF Gain (0-255)</label>
                    <input type="range" id="rfGain" class="input" min="0" max="255" value="128">
                    <div style="text-align: center; margin-top: 5px;">
                        <span id="rfGainValue">128</span>
                    </div>
                </div>
                
                <div>
                    <button onclick="sendCommand()" class="button button-primary">⚡ Envoyer</button>
                    <button onclick="powerOn()" class="button button-success">🔌 ON</button>
                    <button onclick="powerOff()" class="button button-danger">🔌 OFF</button>
                </div>
            </div>

            <!-- Carte d'état du récepteur -->
            <div class="card">
                <h2 class="card-title">📊 État du Récepteur</h2>
                
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-label">Fréquence</div>
                        <div class="status-value" id="currentFreq">0 Hz</div>
                    </div>
                    
                    <div class="status-item">
                        <div class="status-label">Mode</div>
                        <div class="status-value" id="currentMode">FM</div>
                    </div>
                    
                    <div class="status-item">
                        <div class="status-label">RSSI</div>
                        <div class="status-value" id="currentRSSI">0</div>
                    </div>
                    
                    <div class="status-item">
                        <div class="status-label">Alimentation</div>
                        <div class="status-value" id="powerStatus">OFF</div>
                    </div>
                    
                    <div class="status-item">
                        <div class="status-label">RF Gain</div>
                        <div class="status-value" id="currentGain">128</div>
                    </div>
                </div>
                
                <button onclick="refreshStatus()" class="button button-primary" style="width: 100%; margin-top: 20px;">
                    🔄 Actualiser
                </button>
            </div>

            <!-- Carte de scan -->
            <div class="card">
                <h2 class="card-title">📡 Scan de Fréquences</h2>
                
                <div class="form-group">
                    <label class="label">Fréquence de début (Hz)</label>
                    <input type="number" id="scanStart" class="input" value="144000000" placeholder="144000000">
                </div>
                
                <div class="form-group">
                    <label class="label">Fréquence de fin (Hz)</label>
                    <input type="number" id="scanEnd" class="input" value="146000000" placeholder="146000000">
                </div>
                
                <div class="form-group">
                    <label class="label">Pas (Hz)</label>
                    <input type="number" id="scanStep" class="input" value="25000" placeholder="25000">
                </div>
                
                <div>
                    <button onclick="startScan()" class="button button-success">▶️ Démarrer</button>
                    <button onclick="stopScan()" class="button button-danger">⏹️ Arrêter</button>
                </div>
            </div>

            <!-- Carte d'enregistrement audio -->
            <div class="card">
                <h2 class="card-title">🎵 Enregistrement Audio</h2>
                
                <div class="form-group">
                    <label class="label">Type d'audio</label>
                    <select id="audioType" class="select">
                        <option value="AF">AF (Audio Frequency)</option>
                        <option value="IF">IF (Intermediate Frequency)</option>
                    </select>
                </div>
                
                <div>
                    <button onclick="startRecording()" class="button button-success">🔴 Enregistrer</button>
                    <button onclick="stopRecording()" class="button button-danger">⏹️ Arrêter</button>
                </div>
                
                <div id="recordingStatus" class="hidden" style="margin-top: 15px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; color: #856404;">
                    🔴 Enregistrement en cours...
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        // Mise à jour du slider RF Gain
        document.getElementById('rfGain').addEventListener('input', function() {
            document.getElementById('rfGainValue').textContent = this.value;
        });

        // Fonction pour afficher un message
        function showMessage(type, text) {
            const messageEl = document.getElementById('message');
            messageEl.className = `alert alert-${type}`;
            messageEl.textContent = text;
            messageEl.classList.remove('hidden');
            
            setTimeout(() => {
                messageEl.classList.add('hidden');
            }, 5000);
        }

        // Fonction pour formater la fréquence
        function formatFrequency(freq) {
            if (freq >= 1000000) {
                return `${(freq / 1000000).toFixed(3)} MHz`;
            } else if (freq >= 1000) {
                return `${(freq / 1000).toFixed(1)} kHz`;
            }
            return `${freq} Hz`;
        }

        // Envoyer commande
        async function sendCommand() {
            const frequency = parseInt(document.getElementById('frequency').value);
            const mode = document.getElementById('mode').value;
            const rfGain = parseInt(document.getElementById('rfGain').value);
            
            try {
                const response = await fetch(`${API_BASE}/api/command`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ frequency, mode, rf_gain: rfGain })
                });
                
                const data = await response.json();
                showMessage('success', data.message);
                refreshStatus();
            } catch (error) {
                showMessage('error', 'Erreur de communication avec le backend');
            }
        }

        // Power ON
        async function powerOn() {
            try {
                const response = await fetch(`${API_BASE}/api/command`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ power_on: true })
                });
                
                const data = await response.json();
                showMessage('success', data.message);
                refreshStatus();
            } catch (error) {
                showMessage('error', 'Erreur Power ON');
            }
        }

        // Power OFF
        async function powerOff() {
            try {
                const response = await fetch(`${API_BASE}/api/command`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ power_on: false })
                });
                
                const data = await response.json();
                showMessage('success', data.message);
                refreshStatus();
            } catch (error) {
                showMessage('error', 'Erreur Power OFF');
            }
        }

        // Actualiser l'état
        async function refreshStatus() {
            try {
                const response = await fetch(`${API_BASE}/api/status`);
                const data = await response.json();
                
                document.getElementById('currentFreq').textContent = formatFrequency(data.frequency);
                document.getElementById('currentMode').textContent = data.mode;
                document.getElementById('currentRSSI').textContent = data.rssi;
                document.getElementById('powerStatus').textContent = data.power_on ? 'ON' : 'OFF';
                document.getElementById('powerStatus').style.color = data.power_on ? '#28a745' : '#dc3545';
                document.getElementById('currentGain').textContent = data.rf_gain;
            } catch (error) {
                showMessage('error', 'Erreur lecture état');
            }
        }

        // Démarrer scan
        async function startScan() {
            const startFreq = parseInt(document.getElementById('scanStart').value);
            const endFreq = parseInt(document.getElementById('scanEnd').value);
            const step = parseInt(document.getElementById('scanStep').value);
            const mode = document.getElementById('mode').value;
            
            try {
                const response = await fetch(`${API_BASE}/api/scan/start`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        start_frequency: startFreq, 
                        end_frequency: endFreq, 
                        step: step,
                        mode: mode 
                    })
                });
                
                const data = await response.json();
                showMessage('success', data.message);
            } catch (error) {
                showMessage('error', 'Erreur démarrage scan');
            }
        }

        // Arrêter scan
        async function stopScan() {
            try {
                const response = await fetch(`${API_BASE}/api/scan/stop`, {
                    method: 'POST'
                });
                
                const data = await response.json();
                showMessage('success', data.message);
            } catch (error) {
                showMessage('error', 'Erreur arrêt scan');
            }
        }

        // Démarrer enregistrement
        async function startRecording() {
            const audioType = document.getElementById('audioType').value;
            
            try {
                const response = await fetch(`${API_BASE}/api/audio/start`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ audio_type: audioType })
                });
                
                const data = await response.json();
                showMessage('success', data.message);
                document.getElementById('recordingStatus').classList.remove('hidden');
            } catch (error) {
                showMessage('error', 'Erreur démarrage enregistrement');
            }
        }

        // Arrêter enregistrement
        async function stopRecording() {
            try {
                const response = await fetch(`${API_BASE}/api/audio/stop`, {
                    method: 'POST'
                });
                
                const data = await response.json();
                showMessage('success', data.message);
                document.getElementById('recordingStatus').classList.add('hidden');
            } catch (error) {
                showMessage('error', 'Erreur arrêt enregistrement');
            }
        }

        // Actualisation automatique de l'état
        setInterval(refreshStatus, 3000);
        
        // Actualisation initiale
        refreshStatus();
    </script>
</body>
</html>
