"""
Modèles Pydantic pour l'API ICOM IC-R8600
"""

from pydantic import BaseModel, Field
from typing import Optional, Literal
from enum import Enum

class ModulationType(str, Enum):
    LSB = "LSB"
    USB = "USB"
    AM = "AM"
    CW = "CW"
    FM = "FM"
    WFM = "WFM"
    CWR = "CWR"
    RTTY = "RTTY"
    RTTYR = "RTTYR"
    PSK = "PSK"
    PSKR = "PSKR"

class AudioType(str, Enum):
    AF = "AF"  # Audio Frequency
    IF = "IF"  # Intermediate Frequency

class CommandRequest(BaseModel):
    """Modèle pour les commandes envoyées au récepteur"""
    frequency: Optional[int] = Field(None, ge=100000, le=3000000000, description="Fréquence en Hz")
    mode: Optional[ModulationType] = Field(None, description="Mode de modulation")
    rf_gain: Optional[int] = Field(None, ge=0, le=255, description="RF Gain (0-255)")
    filter_width: Optional[int] = Field(None, ge=0, le=10, description="Largeur de filtre")
    power_on: Optional[bool] = Field(None, description="Allumer/éteindre le récepteur")

class ScanRequest(BaseModel):
    """Modèle pour les commandes de scan"""
    start_frequency: int = Field(..., ge=100000, le=3000000000, description="Fréquence de début en Hz")
    end_frequency: int = Field(..., ge=100000, le=3000000000, description="Fréquence de fin en Hz")
    step: int = Field(25000, ge=1000, le=1000000, description="Pas de fréquence en Hz")
    mode: Optional[ModulationType] = Field(ModulationType.FM, description="Mode de modulation pour le scan")

class AudioRecordingRequest(BaseModel):
    """Modèle pour les commandes d'enregistrement audio"""
    audio_type: AudioType = Field(AudioType.AF, description="Type d'audio à enregistrer")
    device_id: Optional[int] = Field(None, description="ID du périphérique audio")

class RadioStatusResponse(BaseModel):
    """Modèle pour l'état du récepteur"""
    frequency: int = Field(..., description="Fréquence actuelle en Hz")
    mode: str = Field(..., description="Mode de modulation actuel")
    rssi: int = Field(..., description="Niveau RSSI")
    power_on: bool = Field(..., description="État d'alimentation")
    rf_gain: int = Field(..., description="RF Gain actuel")
    filter_width: int = Field(..., description="Largeur de filtre actuelle")
    timestamp: str = Field(..., description="Timestamp de la lecture")

class CommandResponse(BaseModel):
    """Modèle pour les réponses aux commandes"""
    success: bool = Field(..., description="Succès de la commande")
    message: str = Field(..., description="Message de retour")
    data: Optional[dict] = Field(None, description="Données additionnelles")

class AudioRecordingResponse(BaseModel):
    """Modèle pour les réponses d'enregistrement audio"""
    success: bool = Field(..., description="Succès de l'opération")
    message: str = Field(..., description="Message de retour")
    filename: Optional[str] = Field(None, description="Nom du fichier")
    path: Optional[str] = Field(None, description="Chemin du fichier")
    duration: Optional[float] = Field(None, description="Durée en secondes")
    file_size: Optional[int] = Field(None, description="Taille du fichier en bytes")

class AudioRecordingStatus(BaseModel):
    """Modèle pour l'état de l'enregistrement"""
    is_recording: bool = Field(..., description="Enregistrement en cours")
    filename: Optional[str] = Field(None, description="Nom du fichier en cours")
    duration: Optional[float] = Field(None, description="Durée actuelle en secondes")
    sample_rate: Optional[int] = Field(None, description="Fréquence d'échantillonnage")
    channels: Optional[int] = Field(None, description="Nombre de canaux")

class RecordingInfo(BaseModel):
    """Modèle pour les informations d'un enregistrement"""
    filename: str = Field(..., description="Nom du fichier")
    path: str = Field(..., description="Chemin du fichier")
    size: int = Field(..., description="Taille en bytes")
    created: str = Field(..., description="Date de création ISO")
    duration: float = Field(..., description="Durée en secondes")
    sample_rate: int = Field(..., description="Fréquence d'échantillonnage")

class AudioDevice(BaseModel):
    """Modèle pour un périphérique audio"""
    id: int = Field(..., description="ID du périphérique")
    name: str = Field(..., description="Nom du périphérique")
    channels: int = Field(..., description="Nombre de canaux d'entrée")
    sample_rate: float = Field(..., description="Fréquence d'échantillonnage par défaut")

class ErrorResponse(BaseModel):
    """Modèle pour les réponses d'erreur"""
    error: str = Field(..., description="Type d'erreur")
    message: str = Field(..., description="Message d'erreur")
    details: Optional[dict] = Field(None, description="Détails additionnels")
