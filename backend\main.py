"""
Serveur FastAPI pour contrôler l'ICOM IC-R8600
Interface REST pour communication CI-V et enregistrement audio
"""

import os
import logging
from datetime import datetime
from typing import List, Optional
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from fastapi.staticfiles import StaticFiles

from models import (
    CommandRequest, CommandResponse, RadioStatusResponse,
    ScanRequest, AudioRecordingRequest, AudioRecordingResponse,
    AudioRecordingStatus, RecordingInfo, AudioDevice, ErrorResponse
)
from icom_handler import ICOMHandler
from audio_recorder import AudioRecorder

# Configuration
app = FastAPI(
    title="ICOM IC-R8600 Controller API",
    description="API REST pour contrôler le récepteur ICOM IC-R8600 via CI-V",
    version="1.0.0"
)

# Configuration CORS pour le frontend React
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # Vite et CRA
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Instances globales
icom_handler = None
audio_recorder = None

# Configuration logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@app.on_event("startup")
async def startup_event():
    """Initialisation au démarrage"""
    global icom_handler, audio_recorder
    
    # Initialiser le handler ICOM
    # Configuration pour votre IC-R8600 via réseau Ethernet
    icom_handler = ICOMHandler(
        port="COM3",  # Port série (non utilisé en UDP)
        baudrate=19200,
        use_udp=True,  # Connexion UDP via Ethernet
        udp_host="*************",  # Nouvelle adresse IP de votre IC-R8600
        udp_port=50001  # Port CI-V par défaut
    )
    
    # Tenter la connexion
    if icom_handler.connect():
        logger.info("Connexion ICOM établie")
    else:
        logger.warning("Impossible de se connecter à l'ICOM")
    
    # Initialiser l'enregistreur audio
    audio_recorder = AudioRecorder()
    logger.info("Enregistreur audio initialisé")

@app.on_event("shutdown")
async def shutdown_event():
    """Nettoyage à l'arrêt"""
    global icom_handler, audio_recorder
    
    if icom_handler:
        icom_handler.disconnect()
    
    if audio_recorder and audio_recorder.is_recording:
        audio_recorder.stop_recording()

# Routes API

@app.get("/", response_model=dict)
async def root():
    """Page d'accueil de l'API"""
    return {
        "message": "ICOM IC-R8600 Controller API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }

@app.post("/api/command", response_model=CommandResponse)
async def send_command(command: CommandRequest):
    """Envoie une commande au récepteur ICOM"""
    if not icom_handler:
        raise HTTPException(status_code=500, detail="Handler ICOM non initialisé")
    
    try:
        results = []
        
        # Traitement des commandes
        if command.power_on is not None:
            if command.power_on:
                success = icom_handler.power_on()
                results.append(f"Power ON: {'OK' if success else 'ERREUR'}")
            else:
                success = icom_handler.power_off()
                results.append(f"Power OFF: {'OK' if success else 'ERREUR'}")
        
        if command.frequency is not None:
            success = icom_handler.set_frequency(command.frequency)
            results.append(f"Fréquence {command.frequency} Hz: {'OK' if success else 'ERREUR'}")
        
        if command.mode is not None:
            success = icom_handler.set_mode(command.mode.value)
            results.append(f"Mode {command.mode.value}: {'OK' if success else 'ERREUR'}")
        
        if command.rf_gain is not None:
            success = icom_handler.set_rf_gain(command.rf_gain)
            results.append(f"RF Gain {command.rf_gain}: {'OK' if success else 'ERREUR'}")
        
        return CommandResponse(
            success=True,
            message="; ".join(results) if results else "Aucune commande spécifiée",
            data={"timestamp": datetime.now().isoformat()}
        )
        
    except Exception as e:
        logger.error(f"Erreur commande: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/scan/start", response_model=CommandResponse)
async def start_scan(scan_request: ScanRequest):
    """Démarre un scan de fréquences"""
    if not icom_handler:
        raise HTTPException(status_code=500, detail="Handler ICOM non initialisé")

    try:
        # Configurer le mode si spécifié
        if scan_request.mode:
            icom_handler.set_mode(scan_request.mode.value)

        success = icom_handler.start_scan(
            scan_request.start_frequency,
            scan_request.end_frequency,
            scan_request.step
        )

        return CommandResponse(
            success=success,
            message=f"Scan {'démarré' if success else 'échec'} de {scan_request.start_frequency} à {scan_request.end_frequency} Hz",
            data={
                "start_freq": scan_request.start_frequency,
                "end_freq": scan_request.end_frequency,
                "step": scan_request.step,
                "mode": scan_request.mode.value if scan_request.mode else None
            }
        )
    except Exception as e:
        logger.error(f"Erreur scan: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/scan/stop", response_model=CommandResponse)
async def stop_scan():
    """Arrête le scan en cours"""
    if not icom_handler:
        raise HTTPException(status_code=500, detail="Handler ICOM non initialisé")

    try:
        success = icom_handler.stop_scan()
        return CommandResponse(
            success=success,
            message=f"Scan {'arrêté' if success else 'échec arrêt'}"
        )
    except Exception as e:
        logger.error(f"Erreur arrêt scan: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Routes Audio

@app.post("/api/audio/start", response_model=AudioRecordingResponse)
async def start_audio_recording(recording_request: AudioRecordingRequest):
    """Démarre l'enregistrement audio"""
    if not audio_recorder:
        raise HTTPException(status_code=500, detail="Enregistreur audio non initialisé")

    try:
        result = audio_recorder.start_recording(
            audio_type=recording_request.audio_type.value,
            device_id=recording_request.device_id
        )

        if result["success"]:
            return AudioRecordingResponse(**result)
        else:
            raise HTTPException(status_code=400, detail=result["message"])

    except Exception as e:
        logger.error(f"Erreur démarrage enregistrement: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/audio/stop", response_model=AudioRecordingResponse)
async def stop_audio_recording():
    """Arrête l'enregistrement audio"""
    if not audio_recorder:
        raise HTTPException(status_code=500, detail="Enregistreur audio non initialisé")

    try:
        result = audio_recorder.stop_recording()

        if result["success"]:
            return AudioRecordingResponse(**result)
        else:
            raise HTTPException(status_code=400, detail=result["message"])

    except Exception as e:
        logger.error(f"Erreur arrêt enregistrement: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/audio/status", response_model=AudioRecordingStatus)
async def get_audio_status():
    """Récupère l'état de l'enregistrement audio"""
    if not audio_recorder:
        raise HTTPException(status_code=500, detail="Enregistreur audio non initialisé")

    try:
        status = audio_recorder.get_recording_status()
        return AudioRecordingStatus(**status)
    except Exception as e:
        logger.error(f"Erreur état enregistrement: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/recordings", response_model=List[RecordingInfo])
async def list_recordings():
    """Liste tous les enregistrements disponibles"""
    if not audio_recorder:
        raise HTTPException(status_code=500, detail="Enregistreur audio non initialisé")

    try:
        recordings = audio_recorder.list_recordings()
        return [RecordingInfo(**rec) for rec in recordings]
    except Exception as e:
        logger.error(f"Erreur listage enregistrements: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/recordings/{filename}")
async def download_recording(filename: str):
    """Télécharge un fichier d'enregistrement"""
    if not audio_recorder:
        raise HTTPException(status_code=500, detail="Enregistreur audio non initialisé")

    try:
        filepath = os.path.join(audio_recorder.recordings_dir, filename)
        if os.path.exists(filepath):
            return FileResponse(
                filepath,
                media_type="audio/wav",
                filename=filename
            )
        else:
            raise HTTPException(status_code=404, detail="Fichier non trouvé")
    except Exception as e:
        logger.error(f"Erreur téléchargement: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/recordings/{filename}", response_model=CommandResponse)
async def delete_recording(filename: str):
    """Supprime un enregistrement"""
    if not audio_recorder:
        raise HTTPException(status_code=500, detail="Enregistreur audio non initialisé")

    try:
        result = audio_recorder.delete_recording(filename)
        return CommandResponse(**result)
    except Exception as e:
        logger.error(f"Erreur suppression: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/audio/devices", response_model=List[AudioDevice])
async def get_audio_devices():
    """Liste les périphériques audio disponibles"""
    if not audio_recorder:
        raise HTTPException(status_code=500, detail="Enregistreur audio non initialisé")

    try:
        devices = audio_recorder.get_audio_devices()
        return [AudioDevice(**device) for device in devices]
    except Exception as e:
        logger.error(f"Erreur listage périphériques: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

@app.get("/api/status", response_model=RadioStatusResponse)
async def get_status():
    """Récupère l'état actuel du récepteur"""
    if not icom_handler:
        raise HTTPException(status_code=500, detail="Handler ICOM non initialisé")
    
    try:
        status = icom_handler.get_status()
        return RadioStatusResponse(
            frequency=status["frequency"],
            mode=status["mode"],
            rssi=status["rssi"],
            power_on=status["power_on"],
            rf_gain=status["rf_gain"],
            filter_width=status["filter_width"],
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"Erreur lecture état: {e}")
        raise HTTPException(status_code=500, detail=str(e))
