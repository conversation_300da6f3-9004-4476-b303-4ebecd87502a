#!/usr/bin/env python3
"""
Serveur simple pour servir l'interface React
"""

import http.server
import socketserver
import os
import webbrowser
from urllib.parse import urlparse

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Ajouter les headers CORS
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_GET(self):
        # Servir index.html pour toutes les routes (SPA)
        if self.path == '/' or not os.path.exists(self.path[1:]):
            self.path = '/index.html'
        return super().do_GET()

def main():
    PORT = 5173
    
    # Changer vers le répertoire frontend
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    print(f"🌐 Démarrage du serveur sur http://localhost:{PORT}")
    print("📁 Répertoire:", os.getcwd())
    
    with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
        print(f"✅ Serveur démarré sur http://localhost:{PORT}")
        print("🔗 Ouvrez votre navigateur sur cette adresse")
        print("⏹️  Appuyez sur Ctrl+C pour arrêter")
        
        # Ouvrir automatiquement le navigateur
        try:
            webbrowser.open(f'http://localhost:{PORT}')
        except:
            pass
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Serveur arrêté")

if __name__ == "__main__":
    main()
