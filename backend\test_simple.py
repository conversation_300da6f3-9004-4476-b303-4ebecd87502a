#!/usr/bin/env python3
"""
Test simple de communication série avec l'IC-R8600
"""

import serial
import time

def test_serial_basic():
    """Test de base de la communication série"""
    print("=== Test Communication Série Basique ===")
    
    try:
        # Ouvrir le port série
        ser = serial.Serial(
            port='COM3',
            baudrate=19200,
            bytesize=serial.EIGHTBITS,
            parity=serial.PARITY_NONE,
            stopbits=serial.STOPBITS_ONE,
            timeout=2.0
        )
        
        print(f"✅ Port {ser.port} ouvert")
        print(f"   Baudrate: {ser.baudrate}")
        print(f"   Timeout: {ser.timeout}s")
        
        # Test 1: Envoyer commande simple de lecture fréquence
        print("\n--- Test 1: Commande lecture fréquence ---")
        cmd = bytes([0xFE, 0xFE, 0x96, 0xE0, 0x03, 0xFD])
        print(f"Envoi: {' '.join([f'{b:02X}' for b in cmd])}")
        
        ser.write(cmd)
        time.sleep(0.5)  # Attendre la réponse
        
        # Lire la réponse
        response = ser.read(50)
        if response:
            print(f"Réponse: {' '.join([f'{b:02X}' for b in response])}")
            print(f"Longueur: {len(response)} bytes")
        else:
            print("❌ Aucune réponse reçue")
        
        # Test 2: Commande simple power status
        print("\n--- Test 2: Commande power status ---")
        cmd = bytes([0xFE, 0xFE, 0x96, 0xE0, 0x18, 0x00, 0xFD])
        print(f"Envoi: {' '.join([f'{b:02X}' for b in cmd])}")
        
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(50)
        if response:
            print(f"Réponse: {' '.join([f'{b:02X}' for b in response])}")
        else:
            print("❌ Aucune réponse reçue")
        
        # Test 3: Echo test (si supporté)
        print("\n--- Test 3: Test d'écho ---")
        cmd = bytes([0xFE, 0xFE, 0x96, 0xE0, 0xFD])  # Commande minimale
        print(f"Envoi: {' '.join([f'{b:02X}' for b in cmd])}")
        
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(50)
        if response:
            print(f"Réponse: {' '.join([f'{b:02X}' for b in response])}")
        else:
            print("❌ Aucune réponse reçue")
        
        # Fermer le port
        ser.close()
        print("\n✅ Port fermé")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_different_addresses():
    """Test avec différentes adresses CI-V"""
    print("\n=== Test Différentes Adresses CI-V ===")
    
    addresses = [0x96, 0x94, 0x98, 0x00]  # Adresses possibles
    
    for addr in addresses:
        print(f"\n--- Test adresse 0x{addr:02X} ---")
        
        try:
            ser = serial.Serial('COM3', 19200, timeout=1.0)
            
            # Commande de lecture fréquence
            cmd = bytes([0xFE, 0xFE, addr, 0xE0, 0x03, 0xFD])
            print(f"Envoi: {' '.join([f'{b:02X}' for b in cmd])}")
            
            ser.write(cmd)
            time.sleep(0.3)
            
            response = ser.read(20)
            if response:
                print(f"✅ Réponse: {' '.join([f'{b:02X}' for b in response])}")
            else:
                print("❌ Pas de réponse")
            
            ser.close()
            
        except Exception as e:
            print(f"❌ Erreur: {e}")

def main():
    """Fonction principale"""
    print("Test Simple IC-R8600")
    print("====================")
    print("Assurez-vous que:")
    print("1. L'IC-R8600 est allumé")
    print("2. Le câble USB est connecté")
    print("3. CI-V est activé sur le récepteur")
    print("4. CI-V Address = 96h")
    print("5. CI-V Baud Rate = 19200")
    print("6. CI-V Transceive = ON")
    print()
    
    # Test de base
    if test_serial_basic():
        print("\n" + "="*50)
        # Test avec différentes adresses
        test_different_addresses()
    
    print("\n" + "="*50)
    print("Tests terminés")
    print("\nSi aucune réponse n'est reçue:")
    print("1. Vérifiez les paramètres CI-V du récepteur")
    print("2. Essayez un autre port COM")
    print("3. Vérifiez que le récepteur est bien allumé")
    print("4. Testez avec un logiciel ICOM officiel")

if __name__ == "__main__":
    main()
