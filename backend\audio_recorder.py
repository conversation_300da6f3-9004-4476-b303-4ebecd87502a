"""
Module d'enregistrement audio pour ICOM IC-R8600
Capture AF (Audio Frequency) ou IF (Intermediate Frequency)
"""

import sounddevice as sd
import soundfile as sf
import numpy as np
import threading
import time
import os
from datetime import datetime
from typing import Optional, Dict, Any
import logging

class AudioRecorder:
    def __init__(self, sample_rate: int = 48000, channels: int = 1, 
                 recordings_dir: str = "recordings"):
        self.sample_rate = sample_rate
        self.channels = channels
        self.recordings_dir = recordings_dir
        self.is_recording = False
        self.current_recording = None
        self.recording_thread = None
        self.audio_data = []
        
        # Créer le dossier d'enregistrements
        os.makedirs(recordings_dir, exist_ok=True)
        
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Lister les périphériques audio disponibles
        self._list_audio_devices()
    
    def _list_audio_devices(self):
        """Liste les périphériques audio disponibles"""
        try:
            devices = sd.query_devices()
            self.logger.info("Périphériques audio disponibles:")
            for i, device in enumerate(devices):
                self.logger.info(f"  {i}: {device['name']} - {device['max_input_channels']} entrées")
        except Exception as e:
            self.logger.error(f"Erreur listage périphériques: {e}")
    
    def _audio_callback(self, indata, frames, time, status):
        """Callback pour la capture audio en temps réel"""
        if status:
            self.logger.warning(f"Status audio: {status}")
        
        if self.is_recording:
            # Convertir en float32 et ajouter aux données
            audio_chunk = indata.copy().flatten()
            self.audio_data.extend(audio_chunk)
    
    def start_recording(self, audio_type: str = "AF", device_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Démarre l'enregistrement audio
        
        Args:
            audio_type: "AF" (Audio Frequency) ou "IF" (Intermediate Frequency)
            device_id: ID du périphérique audio (None = défaut)
        """
        if self.is_recording:
            return {"success": False, "message": "Enregistrement déjà en cours"}
        
        try:
            # Générer nom de fichier avec timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"icom_r8600_{audio_type}_{timestamp}.wav"
            self.current_recording = os.path.join(self.recordings_dir, filename)
            
            # Réinitialiser les données audio
            self.audio_data = []
            self.is_recording = True
            
            # Démarrer le stream audio
            self.stream = sd.InputStream(
                device=device_id,
                channels=self.channels,
                samplerate=self.sample_rate,
                callback=self._audio_callback,
                dtype=np.float32
            )
            
            self.stream.start()
            
            self.logger.info(f"Enregistrement démarré: {filename}")
            return {
                "success": True,
                "message": f"Enregistrement {audio_type} démarré",
                "filename": filename,
                "path": self.current_recording
            }
            
        except Exception as e:
            self.logger.error(f"Erreur démarrage enregistrement: {e}")
            self.is_recording = False
            return {"success": False, "message": f"Erreur: {str(e)}"}
    
    def stop_recording(self) -> Dict[str, Any]:
        """Arrête l'enregistrement et sauvegarde le fichier"""
        if not self.is_recording:
            return {"success": False, "message": "Aucun enregistrement en cours"}
        
        try:
            self.is_recording = False
            
            # Arrêter le stream
            if hasattr(self, 'stream'):
                self.stream.stop()
                self.stream.close()
            
            # Sauvegarder les données audio
            if self.audio_data and self.current_recording:
                audio_array = np.array(self.audio_data, dtype=np.float32)
                
                # Reshape pour le bon nombre de canaux
                if self.channels > 1:
                    audio_array = audio_array.reshape(-1, self.channels)
                
                # Sauvegarder en WAV
                sf.write(
                    self.current_recording,
                    audio_array,
                    self.sample_rate,
                    subtype='PCM_16'
                )
                
                duration = len(self.audio_data) / self.sample_rate
                file_size = os.path.getsize(self.current_recording)
                
                self.logger.info(f"Enregistrement sauvegardé: {self.current_recording}")
                self.logger.info(f"Durée: {duration:.2f}s, Taille: {file_size} bytes")
                
                result = {
                    "success": True,
                    "message": "Enregistrement terminé et sauvegardé",
                    "filename": os.path.basename(self.current_recording),
                    "path": self.current_recording,
                    "duration": duration,
                    "file_size": file_size,
                    "sample_rate": self.sample_rate
                }
                
                # Réinitialiser
                self.current_recording = None
                self.audio_data = []
                
                return result
            else:
                return {"success": False, "message": "Aucune donnée audio capturée"}
                
        except Exception as e:
            self.logger.error(f"Erreur arrêt enregistrement: {e}")
            return {"success": False, "message": f"Erreur: {str(e)}"}
    
    def get_recording_status(self) -> Dict[str, Any]:
        """Retourne l'état de l'enregistrement"""
        if self.is_recording:
            duration = len(self.audio_data) / self.sample_rate if self.audio_data else 0
            return {
                "is_recording": True,
                "filename": os.path.basename(self.current_recording) if self.current_recording else None,
                "duration": duration,
                "sample_rate": self.sample_rate,
                "channels": self.channels
            }
        else:
            return {"is_recording": False}
    
    def list_recordings(self) -> List[Dict[str, Any]]:
        """Liste tous les enregistrements disponibles"""
        recordings = []
        
        try:
            for filename in os.listdir(self.recordings_dir):
                if filename.endswith('.wav'):
                    filepath = os.path.join(self.recordings_dir, filename)
                    stat = os.stat(filepath)
                    
                    # Lire les métadonnées audio
                    try:
                        info = sf.info(filepath)
                        duration = info.duration
                        sample_rate = info.samplerate
                    except:
                        duration = 0
                        sample_rate = 0
                    
                    recordings.append({
                        "filename": filename,
                        "path": filepath,
                        "size": stat.st_size,
                        "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        "duration": duration,
                        "sample_rate": sample_rate
                    })
            
            # Trier par date de création (plus récent en premier)
            recordings.sort(key=lambda x: x['created'], reverse=True)
            
        except Exception as e:
            self.logger.error(f"Erreur listage enregistrements: {e}")
        
        return recordings
    
    def delete_recording(self, filename: str) -> Dict[str, Any]:
        """Supprime un enregistrement"""
        try:
            filepath = os.path.join(self.recordings_dir, filename)
            if os.path.exists(filepath):
                os.remove(filepath)
                return {"success": True, "message": f"Fichier {filename} supprimé"}
            else:
                return {"success": False, "message": "Fichier non trouvé"}
        except Exception as e:
            return {"success": False, "message": f"Erreur: {str(e)}"}
    
    def get_audio_devices(self) -> List[Dict[str, Any]]:
        """Retourne la liste des périphériques audio"""
        devices = []
        try:
            device_list = sd.query_devices()
            for i, device in enumerate(device_list):
                if device['max_input_channels'] > 0:  # Seulement les entrées
                    devices.append({
                        "id": i,
                        "name": device['name'],
                        "channels": device['max_input_channels'],
                        "sample_rate": device['default_samplerate']
                    })
        except Exception as e:
            self.logger.error(f"Erreur listage périphériques: {e}")
        
        return devices
