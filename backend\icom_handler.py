"""
Module de communication CI-V pour ICOM IC-R8600
Basé sur le CI-V Reference Guide officiel
"""

import serial
import socket
import time
import logging
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

# Configuration CI-V
ICOM_ADDRESS = 0x96  # IC-R8600
CONTROLLER_ADDRESS = 0xDF  # Adresse contrôleur (DFh)
PREAMBLE = [0xFE, 0xFE]
POSTAMBLE = 0xFD

# Modes de modulation
MODES = {
    'LSB': 0x00, 'USB': 0x01, 'AM': 0x02, 'CW': 0x03,
    'FM': 0x05, 'WFM': 0x06, 'CWR': 0x07, 'RTTY': 0x08,
    'RTTYR': 0x09, 'PSK': 0x12, 'PSKR': 0x13
}

@dataclass
class RadioStatus:
    frequency: int = 0
    mode: str = "FM"
    rssi: int = 0
    power_on: bool = False
    rf_gain: int = 128
    filter_width: int = 0

class ICOMHandler:
    def __init__(self, port: str = "/dev/ttyUSB0", baudrate: int = 19200, 
                 use_udp: bool = False, udp_host: str = "*************", udp_port: int = 50001):
        self.port = port
        self.baudrate = baudrate
        self.use_udp = use_udp
        self.udp_host = udp_host
        self.udp_port = udp_port
        self.serial_conn = None
        self.udp_socket = None
        self.status = RadioStatus()
        
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def connect(self) -> bool:
        """Établit la connexion série ou UDP"""
        try:
            if self.use_udp:
                self.udp_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                self.logger.info(f"Connexion UDP vers {self.udp_host}:{self.udp_port}")
                return True
            else:
                self.serial_conn = serial.Serial(
                    port=self.port,
                    baudrate=self.baudrate,
                    bytesize=serial.EIGHTBITS,
                    parity=serial.PARITY_NONE,
                    stopbits=serial.STOPBITS_ONE,
                    timeout=2.0
                )
                self.logger.info(f"Connexion série sur {self.port} à {self.baudrate} bauds")
                return True
        except Exception as e:
            self.logger.error(f"Erreur de connexion: {e}")
            return False
    
    def disconnect(self):
        """Ferme la connexion"""
        if self.serial_conn and self.serial_conn.is_open:
            self.serial_conn.close()
        if self.udp_socket:
            self.udp_socket.close()
    
    def _calculate_checksum(self, data: List[int]) -> int:
        """Calcule le checksum pour les commandes CI-V (optionnel)"""
        return sum(data) & 0xFF
    
    def _build_command(self, command: List[int]) -> bytes:
        """Construit une commande CI-V complète"""
        cmd = PREAMBLE + [ICOM_ADDRESS, CONTROLLER_ADDRESS] + command + [POSTAMBLE]
        return bytes(cmd)
    
    def _send_command(self, command: bytes) -> Optional[bytes]:
        """Envoie une commande et lit la réponse"""
        try:
            if self.use_udp and self.udp_socket:
                self.udp_socket.sendto(command, (self.udp_host, self.udp_port))
                response, _ = self.udp_socket.recvfrom(1024)
                return response
            elif self.serial_conn and self.serial_conn.is_open:
                self.serial_conn.write(command)
                time.sleep(0.1)  # Délai pour la réponse
                response = self.serial_conn.read(50)
                return response if response else None
        except Exception as e:
            self.logger.error(f"Erreur envoi commande: {e}")
            return None
    
    def power_on(self) -> bool:
        """Allume le récepteur"""
        command = self._build_command([0x18, 0x01])
        response = self._send_command(command)
        if response:
            self.status.power_on = True
            return True
        return False
    
    def power_off(self) -> bool:
        """Éteint le récepteur"""
        command = self._build_command([0x18, 0x00])
        response = self._send_command(command)
        if response:
            self.status.power_on = False
            return True
        return False
    
    def set_frequency(self, freq_hz: int) -> bool:
        """Définit la fréquence en Hz"""
        # Conversion fréquence en format BCD ICOM
        freq_str = f"{freq_hz:010d}"
        bcd_bytes = []
        
        # Conversion en BCD (2 chiffres par byte, ordre inversé)
        for i in range(4, -1, -1):
            digit1 = int(freq_str[i*2])
            digit2 = int(freq_str[i*2 + 1])
            bcd_bytes.append((digit1 << 4) | digit2)
        
        command = self._build_command([0x05] + bcd_bytes)
        response = self._send_command(command)
        
        if response:
            self.status.frequency = freq_hz
            return True
        return False
    
    def get_frequency(self) -> Optional[int]:
        """Lit la fréquence actuelle"""
        command = self._build_command([0x03])
        response = self._send_command(command)
        
        if response and len(response) >= 11:
            # Décodage BCD de la réponse
            freq_bytes = response[6:11]  # 5 bytes de fréquence
            freq_str = ""
            
            for byte in reversed(freq_bytes):
                digit1 = (byte >> 4) & 0x0F
                digit2 = byte & 0x0F
                freq_str += f"{digit1}{digit2}"
            
            try:
                frequency = int(freq_str)
                self.status.frequency = frequency
                return frequency
            except ValueError:
                pass
        
        return None
    
    def set_mode(self, mode: str) -> bool:
        """Définit le mode de modulation"""
        if mode not in MODES:
            return False
        
        mode_code = MODES[mode]
        command = self._build_command([0x06, mode_code])
        response = self._send_command(command)
        
        if response:
            self.status.mode = mode
            return True
        return False
    
    def set_rf_gain(self, gain: int) -> bool:
        """Définit le RF gain (0-255)"""
        if not 0 <= gain <= 255:
            return False
        
        command = self._build_command([0x14, 0x02, gain])
        response = self._send_command(command)
        
        if response:
            self.status.rf_gain = gain
            return True
        return False
    
    def get_rssi(self) -> Optional[int]:
        """Lit le niveau RSSI"""
        command = self._build_command([0x15, 0x02])
        response = self._send_command(command)
        
        if response and len(response) >= 8:
            rssi_bytes = response[6:8]
            rssi = (rssi_bytes[0] << 8) | rssi_bytes[1]
            self.status.rssi = rssi
            return rssi
        
        return None
    
    def start_scan(self, start_freq: int, end_freq: int, step: int = 25000) -> bool:
        """Démarre un scan entre deux fréquences"""
        # Configuration des limites de scan (commandes spécifiques IC-R8600)
        # Cette implémentation est simplifiée
        self.logger.info(f"Scan de {start_freq} à {end_freq} Hz, pas de {step} Hz")
        
        # Commande de démarrage du scan
        command = self._build_command([0x0E, 0x01])
        response = self._send_command(command)
        
        return response is not None
    
    def stop_scan(self) -> bool:
        """Arrête le scan"""
        command = self._build_command([0x0E, 0x00])
        response = self._send_command(command)
        return response is not None
    
    def get_status(self) -> Dict[str, Any]:
        """Retourne l'état complet du récepteur"""
        # Mise à jour des valeurs actuelles
        self.get_frequency()
        self.get_rssi()
        
        return {
            "frequency": self.status.frequency,
            "mode": self.status.mode,
            "rssi": self.status.rssi,
            "power_on": self.status.power_on,
            "rf_gain": self.status.rf_gain,
            "filter_width": self.status.filter_width
        }
