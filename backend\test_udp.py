#!/usr/bin/env python3
"""
Test de communication UDP avec l'IC-R8600 via Ethernet
"""

import socket
import time
import sys

def test_udp_connection():
    """Test de connexion UDP basique"""
    print("=== Test Communication UDP IC-R8600 ===")
    print(f"Adresse IP: ***********")
    print(f"Port CI-V: 50001")
    print()
    
    try:
        # Créer socket UDP
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(3.0)  # Timeout de 3 secondes
        
        print("✅ Socket UDP créé")
        
        # Test 1: Commande lecture fréquence
        print("\n--- Test 1: Lecture fréquence ---")
        cmd = bytes([0xFE, 0xFE, 0x96, 0xE0, 0x03, 0xFD])
        print(f"Envoi: {' '.join([f'{b:02X}' for b in cmd])}")
        
        sock.sendto(cmd, ("***********", 50001))
        
        try:
            response, addr = sock.recvfrom(1024)
            print(f"✅ Réponse reçue de {addr}")
            print(f"   Données: {' '.join([f'{b:02X}' for b in response])}")
            print(f"   Longueur: {len(response)} bytes")
            
            # Analyser la réponse
            if len(response) >= 6 and response[0:2] == bytes([0xFE, 0xFE]):
                print("   ✅ Format CI-V valide")
                if response[2] == 0xE0 and response[3] == 0x96:  # Réponse du récepteur
                    print("   ✅ Réponse de l'IC-R8600")
                    if response[4] == 0x03:  # Commande fréquence
                        print("   ✅ Réponse à la commande fréquence")
                        if len(response) >= 11:
                            # Décoder la fréquence BCD
                            freq_bytes = response[5:10]
                            freq_str = ""
                            for byte in reversed(freq_bytes):
                                digit1 = (byte >> 4) & 0x0F
                                digit2 = byte & 0x0F
                                freq_str += f"{digit1}{digit2}"
                            
                            try:
                                frequency = int(freq_str)
                                print(f"   📻 Fréquence: {frequency} Hz ({frequency/1000000:.3f} MHz)")
                            except ValueError:
                                print(f"   ⚠️  Fréquence non décodable: {freq_str}")
            else:
                print("   ⚠️  Format de réponse inattendu")
                
        except socket.timeout:
            print("❌ Timeout - Aucune réponse reçue")
            print("   Vérifiez que:")
            print("   - L'IC-R8600 est allumé")
            print("   - CI-V over LAN est activé")
            print("   - Le port 50001 est ouvert")
        
        # Test 2: Commande RSSI
        print("\n--- Test 2: Lecture RSSI ---")
        cmd = bytes([0xFE, 0xFE, 0x96, 0xE0, 0x15, 0x02, 0xFD])
        print(f"Envoi: {' '.join([f'{b:02X}' for b in cmd])}")
        
        sock.sendto(cmd, ("***********", 50001))
        
        try:
            response, addr = sock.recvfrom(1024)
            print(f"✅ Réponse RSSI: {' '.join([f'{b:02X}' for b in response])}")
            
            if len(response) >= 8:
                rssi_bytes = response[5:7]  # 2 bytes RSSI
                rssi = (rssi_bytes[0] << 8) | rssi_bytes[1] if len(rssi_bytes) >= 2 else rssi_bytes[0]
                print(f"   📊 RSSI: {rssi}")
                
        except socket.timeout:
            print("❌ Timeout RSSI")
        
        # Test 3: Commande Power Status
        print("\n--- Test 3: État alimentation ---")
        cmd = bytes([0xFE, 0xFE, 0x96, 0xE0, 0x18, 0x00, 0xFD])
        print(f"Envoi: {' '.join([f'{b:02X}' for b in cmd])}")
        
        sock.sendto(cmd, ("***********", 50001))
        
        try:
            response, addr = sock.recvfrom(1024)
            print(f"✅ Réponse Power: {' '.join([f'{b:02X}' for b in response])}")
            
        except socket.timeout:
            print("❌ Timeout Power Status")
        
        sock.close()
        print("\n✅ Socket fermé")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_network_connectivity():
    """Test de connectivité réseau"""
    print("\n=== Test Connectivité Réseau ===")
    
    import subprocess
    
    try:
        # Test ping
        result = subprocess.run(
            ["ping", "-n", "3", "***********"], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ Ping réussi")
            # Extraire les temps de réponse
            lines = result.stdout.split('\n')
            for line in lines:
                if 'temps=' in line:
                    print(f"   {line.strip()}")
        else:
            print("❌ Ping échoué")
            print(result.stdout)
            
    except Exception as e:
        print(f"❌ Erreur ping: {e}")

def test_port_connectivity():
    """Test de connectivité du port 50001"""
    print("\n=== Test Port 50001 ===")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5.0)
        
        result = sock.connect_ex(("***********", 50001))
        
        if result == 0:
            print("✅ Port 50001 ouvert (TCP)")
        else:
            print("⚠️  Port 50001 fermé ou filtré (TCP)")
            print("   Note: CI-V utilise UDP, pas TCP")
        
        sock.close()
        
    except Exception as e:
        print(f"❌ Erreur test port: {e}")

def main():
    """Fonction principale"""
    print("Test Communication UDP IC-R8600")
    print("================================")
    print("Configuration réseau détectée:")
    print("  IP IC-R8600: ***********")
    print("  Masque: *************")
    print("  Passerelle: **********")
    print()
    
    # Tests de connectivité
    test_network_connectivity()
    test_port_connectivity()
    
    # Test communication CI-V
    print("\n" + "="*50)
    success = test_udp_connection()
    
    print("\n" + "="*50)
    if success:
        print("✅ Tests terminés avec succès")
        print("\nSi la communication CI-V fonctionne, vous pouvez maintenant:")
        print("1. Lancer le backend: python main.py")
        print("2. Lancer le frontend: npm run dev")
        print("3. Ouvrir http://localhost:5173")
    else:
        print("❌ Problèmes détectés")
        print("\nVérifiez sur l'IC-R8600:")
        print("1. MENU → SET → Connectors → LAN")
        print("2. CI-V over LAN: ON")
        print("3. Control Port: 50001")
        print("4. CI-V Address: 96h")

if __name__ == "__main__":
    main()
